import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { useEffect, useState } from "react";
import { useLocationFilter } from "./useLocationFilter";

export interface DashboardStats {
  todayAppointments: number;
  todayAppointmentsChange: number;
  newPatients: number;
  newPatientsChange: number;
  totalProcedures: number;
  totalProceduresChange: number;
  revenue: number;
  revenueChange: number;
}

export interface PatientDemographics {
  ageGroups: Array<{ label: string; value: number }>;
  genderDistribution: Array<{ label: string; value: number }>;
}

export interface AppointmentMetrics {
  byStatus: Array<{ label: string; value: number }>;
  byDepartment: Array<{ label: string; value: number }>;
}

interface SimpleAppointment {
  id: string;
  appointment_date: string;
  status: string;
}

interface SimplePatient {
  id: string;
  created_at: string;
  date_of_birth: string | null;
  gender: string | null;
}

export function useLocationAnalytics() {
  const { user, organization } = useAuth();
  const { selectedLocation } = useLocations();
  const locationFilter = useLocationFilter();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    todayAppointments: 0,
    todayAppointmentsChange: 0,
    newPatients: 0,
    newPatientsChange: 0,
    totalProcedures: 0,
    totalProceduresChange: 0,
    revenue: 0,
    revenueChange: 0,
  });
  const [patientDemographics, setPatientDemographics] = useState<PatientDemographics>({
    ageGroups: [],
    genderDistribution: [],
  });
  const [appointmentMetrics, setAppointmentMetrics] = useState<AppointmentMetrics>({
    byStatus: [],
    byDepartment: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user || !organization || !selectedLocation) {
      setIsLoading(false);
      return;
    }

    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const today = new Date();
        const todayDateStr = today.toISOString().split("T")[0];
        const tomorrowDateStr = new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString().split("T")[0];

        // Fetch today's appointments with proper location filtering
        let todayAppointmentsQuery = supabase.from("appointments");

        // Apply location-aware filtering and selection
        if (locationFilter.shouldFilterLocation && locationFilter.locationId) {
          // Need location filtering - use inner join with departments
          todayAppointmentsQuery = todayAppointmentsQuery
            .select("id, appointment_date, status, departments!inner(location_id)")
            .eq("departments.location_id", locationFilter.locationId);
        } else {
          // No location filtering needed
          todayAppointmentsQuery = todayAppointmentsQuery
            .select("id, appointment_date, status");
        }

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          todayAppointmentsQuery = todayAppointmentsQuery.eq("organization_id", locationFilter.organizationId);
        }

        // Apply date filters
        todayAppointmentsQuery = todayAppointmentsQuery
          .gte("appointment_date", todayDateStr)
          .lt("appointment_date", tomorrowDateStr);

        const { data: todayAppointments, error: todayError } = await todayAppointmentsQuery;

        if (todayError) throw new Error(todayError.message);

        // Fetch yesterday's appointments for comparison
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const yesterdayDateStr = yesterday.toISOString().split("T")[0];

        let yesterdayAppointmentsQuery = supabase.from("appointments");

        // Apply location-aware filtering and selection
        if (locationFilter.shouldFilterLocation && locationFilter.locationId) {
          // Need location filtering - use inner join with departments
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
            .select("id, departments!inner(location_id)")
            .eq("departments.location_id", locationFilter.locationId);
        } else {
          // No location filtering needed
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
            .select("id");
        }

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery.eq("organization_id", locationFilter.organizationId);
        }

        // Apply date filters
        yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
          .gte("appointment_date", yesterdayDateStr)
          .lt("appointment_date", todayDateStr);

        const { data: yesterdayAppointments, error: yesterdayError } = await yesterdayAppointmentsQuery;

        if (yesterdayError) throw new Error(yesterdayError.message);

        // Fetch patients - for location filtering, we'll get all org patients and filter by appointments later
        let patientsQuery = supabase
          .from("patients")
          .select("id, created_at, date_of_birth, gender");

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          patientsQuery = patientsQuery.eq("organization_id", locationFilter.organizationId);
        }

        const { data: allPatients, error: patientsError } = await patientsQuery;

        if (patientsError) throw new Error(patientsError.message);

        // Calculate stats
        const todayCount = (todayAppointments as SimpleAppointment[])?.length || 0;
        const yesterdayCount = (yesterdayAppointments as SimpleAppointment[])?.length || 0;
        const todayChange = yesterdayCount > 0 ? ((todayCount - yesterdayCount) / yesterdayCount) * 100 : 0;

        // Calculate new patients (last 7 days)
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const newPatients = (patients as SimplePatient[])?.filter(
          (patient: SimplePatient) => new Date(patient.created_at) >= lastWeek
        ).length || 0;

        // Calculate demographics
        const ageGroups = calculateAgeGroups(patients as SimplePatient[]);
        const genderDistribution = calculateGenderDistribution(patients as SimplePatient[]);

        // Fetch medical records (procedures) with location filtering
        let medicalRecordsQuery = supabase.from("medical_records");

        if (locationFilter.shouldFilterLocation && locationFilter.locationId) {
          // Filter medical records by department location
          medicalRecordsQuery = medicalRecordsQuery
            .select(`
              id, visit_date, created_at,
              departments!inner(location_id)
            `)
            .eq("departments.location_id", locationFilter.locationId);
        } else {
          // No location filtering
          medicalRecordsQuery = medicalRecordsQuery
            .select("id, visit_date, created_at");
        }

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          medicalRecordsQuery = medicalRecordsQuery.eq("organization_id", locationFilter.organizationId);
        }

        const { data: medicalRecords, error: medicalRecordsError } = await medicalRecordsQuery;

        if (medicalRecordsError) {
          console.warn("Error fetching medical records:", medicalRecordsError.message);
        }

        // Fetch claims (revenue) - since claims don't have direct location relationship,
        // we'll get all paid claims and filter by provider location if needed
        let claimsQuery = supabase
          .from("claims")
          .select(`
            id, amount, service_date, status, provider_id,
            healthcare_providers!inner(department_id)
          `)
          .eq("status", "paid");

        const { data: allClaims, error: claimsError } = await claimsQuery;

        if (claimsError) {
          console.warn("Error fetching claims:", claimsError.message);
        }

        // Filter claims by location if needed
        let claims = allClaims || [];
        if (locationFilter.shouldFilterLocation && locationFilter.locationId && allClaims) {
          // Get department IDs for this location
          const { data: locationDepartments } = await supabase
            .from("departments")
            .select("id")
            .eq("location_id", locationFilter.locationId);

          const departmentIds = locationDepartments?.map(d => d.id) || [];

          // Filter claims by provider's department
          claims = allClaims.filter(claim =>
            claim.healthcare_providers?.department_id &&
            departmentIds.includes(claim.healthcare_providers.department_id)
          );
        }

        if (claimsError) {
          console.warn("Error fetching claims:", claimsError.message);
        }

        // Calculate procedures (medical records count)
        const totalProcedures = medicalRecords?.length || 0;

        // Calculate procedures yesterday for comparison
        const proceduresYesterday = medicalRecords?.filter(mr => {
          const visitDate = new Date(mr.visit_date).toDateString();
          return visitDate === yesterday.toDateString();
        }).length || 0;

        const proceduresToday = medicalRecords?.filter(mr => {
          const visitDate = new Date(mr.visit_date).toDateString();
          return visitDate === today.toDateString();
        }).length || 0;

        const proceduresChange = proceduresYesterday > 0 ?
          Math.round(((proceduresToday - proceduresYesterday) / proceduresYesterday) * 100) : 0;

        // Calculate revenue from claims
        const totalRevenue = claims?.reduce((sum, claim) => sum + Number(claim.amount), 0) || 0;

        // Calculate revenue yesterday for comparison
        const revenueYesterday = claims?.filter(claim => {
          const serviceDate = new Date(claim.service_date).toDateString();
          return serviceDate === yesterday.toDateString();
        }).reduce((sum, claim) => sum + Number(claim.amount), 0) || 0;

        const revenueToday = claims?.filter(claim => {
          const serviceDate = new Date(claim.service_date).toDateString();
          return serviceDate === today.toDateString();
        }).reduce((sum, claim) => sum + Number(claim.amount), 0) || 0;

        const revenueChange = revenueYesterday > 0 ?
          Math.round(((revenueToday - revenueYesterday) / revenueYesterday) * 100) : 0;

        // Filter patients by location if needed (through appointments)
        let patients = allPatients || [];
        if (locationFilter.shouldFilterLocation && locationFilter.locationId && allPatients) {
          // Get patients who have appointments at this location
          const { data: locationAppointments } = await supabase
            .from("appointments")
            .select(`
              patient_id,
              departments!inner(location_id)
            `)
            .eq("departments.location_id", locationFilter.locationId);

          const patientIds = [...new Set(locationAppointments?.map(a => a.patient_id) || [])];
          patients = allPatients.filter(p => patientIds.includes(p.id));
        }

        // Calculate new patients change (compare today vs yesterday)
        const newPatientsToday = patients?.filter(p => {
          const createdDate = new Date(p.created_at).toDateString();
          return createdDate === today.toDateString();
        }).length || 0;

        const newPatientsYesterday = patients?.filter(p => {
          const createdDate = new Date(p.created_at).toDateString();
          return createdDate === yesterday.toDateString();
        }).length || 0;

        const newPatientsChange = newPatientsYesterday > 0 ?
          Math.round(((newPatientsToday - newPatientsYesterday) / newPatientsYesterday) * 100) : 0;

        // Calculate demographics using filtered patients
        const ageGroups = calculateAgeGroups(patients as SimplePatient[]);
        const genderDistribution = calculateGenderDistribution(patients as SimplePatient[]);

        // Calculate appointment metrics
        const statusMetrics = calculateStatusMetrics(todayAppointments as SimpleAppointment[]);

        setDashboardStats({
          todayAppointments: todayCount,
          todayAppointmentsChange: Math.round(todayChange),
          newPatients: newPatientsToday, // Show today's new patients
          newPatientsChange,
          totalProcedures,
          totalProceduresChange: proceduresChange,
          revenue: totalRevenue,
          revenueChange,
        });

        setPatientDemographics({
          ageGroups,
          genderDistribution,
        });

        setAppointmentMetrics({
          byStatus: statusMetrics,
          byDepartment: [], // Simplified for now
        });

      } catch (err) {
        console.error("Error fetching analytics:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch analytics");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [user, organization, selectedLocation, locationFilter]);

  return {
    dashboardStats,
    patientDemographics,
    appointmentMetrics,
    isLoading,
    error,
  };
}

function calculateAgeGroups(patients: SimplePatient[]): Array<{ label: string; value: number }> {
  if (!patients?.length) return [];

  const ageGroups = {
    "0-17": 0,
    "18-34": 0,
    "35-54": 0,
    "55-74": 0,
    "75+": 0,
  };

  patients.forEach((patient: SimplePatient) => {
    if (!patient.date_of_birth) return;

    const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();

    if (age <= 17) ageGroups["0-17"]++;
    else if (age <= 34) ageGroups["18-34"]++;
    else if (age <= 54) ageGroups["35-54"]++;
    else if (age <= 74) ageGroups["55-74"]++;
    else ageGroups["75+"]++;
  });

  return Object.entries(ageGroups).map(([label, value]) => ({ label, value }));
}

function calculateGenderDistribution(patients: SimplePatient[]): Array<{ label: string; value: number }> {
  if (!patients?.length) return [];

  const genderCounts = patients.reduce((acc: Record<string, number>, patient: SimplePatient) => {
    const gender = patient.gender || "Unknown";
    acc[gender] = (acc[gender] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(genderCounts).map(([label, value]) => ({ label, value }));
}

function calculateStatusMetrics(appointments: SimpleAppointment[]): Array<{ label: string; value: number }> {
  if (!appointments?.length) return [];

  const statusCounts = appointments.reduce((acc: Record<string, number>, appointment: SimpleAppointment) => {
    const status = appointment.status || "Unknown";
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(statusCounts).map(([label, value]) => ({ label, value }));
}