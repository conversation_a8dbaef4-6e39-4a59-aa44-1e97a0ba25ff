import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { useEffect, useState } from "react";
import { applyLocationFilter, useLocationFilter } from "./useLocationFilter";

export interface DashboardStats {
  todayAppointments: number;
  todayAppointmentsChange: number;
  newPatients: number;
  newPatientsChange: number;
  totalProcedures: number;
  totalProceduresChange: number;
  revenue: number;
  revenueChange: number;
}

export interface PatientDemographics {
  ageGroups: Array<{ label: string; value: number }>;
  genderDistribution: Array<{ label: string; value: number }>;
}

export interface AppointmentMetrics {
  byStatus: Array<{ label: string; value: number }>;
  byDepartment: Array<{ label: string; value: number }>;
}

interface SimpleAppointment {
  id: string;
  appointment_date: string;
  status: string;
}

interface SimplePatient {
  id: string;
  created_at: string;
  date_of_birth: string | null;
  gender: string | null;
}

export function useLocationAnalytics() {
  const { user, organization } = useAuth();
  const { selectedLocation } = useLocations();
  const locationFilter = useLocationFilter();
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    todayAppointments: 0,
    todayAppointmentsChange: 0,
    newPatients: 0,
    newPatientsChange: 0,
    totalProcedures: 0,
    totalProceduresChange: 0,
    revenue: 0,
    revenueChange: 0,
  });
  const [patientDemographics, setPatientDemographics] = useState<PatientDemographics>({
    ageGroups: [],
    genderDistribution: [],
  });
  const [appointmentMetrics, setAppointmentMetrics] = useState<AppointmentMetrics>({
    byStatus: [],
    byDepartment: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user || !organization || !selectedLocation) {
      setIsLoading(false);
      return;
    }

    const fetchAnalytics = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const today = new Date();
        const todayDateStr = today.toISOString().split("T")[0];
        const tomorrowDateStr = new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString().split("T")[0];

        // Fetch today's appointments with proper location filtering
        let todayAppointmentsQuery = supabase.from("appointments");

        // Apply location-aware filtering and selection
        if (locationFilter.shouldFilterLocation && locationFilter.locationId) {
          // Need location filtering - use inner join with departments
          todayAppointmentsQuery = todayAppointmentsQuery
            .select("id, appointment_date, status, departments!inner(location_id)")
            .eq("departments.location_id", locationFilter.locationId);
        } else {
          // No location filtering needed
          todayAppointmentsQuery = todayAppointmentsQuery
            .select("id, appointment_date, status");
        }

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          todayAppointmentsQuery = todayAppointmentsQuery.eq("organization_id", locationFilter.organizationId);
        }

        // Apply date filters
        todayAppointmentsQuery = todayAppointmentsQuery
          .gte("appointment_date", todayDateStr)
          .lt("appointment_date", tomorrowDateStr);

        const { data: todayAppointments, error: todayError } = await todayAppointmentsQuery;

        if (todayError) throw new Error(todayError.message);

        // Fetch yesterday's appointments for comparison
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const yesterdayDateStr = yesterday.toISOString().split("T")[0];

        let yesterdayAppointmentsQuery = supabase.from("appointments");

        // Apply location-aware filtering and selection
        if (locationFilter.shouldFilterLocation && locationFilter.locationId) {
          // Need location filtering - use inner join with departments
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
            .select("id, departments!inner(location_id)")
            .eq("departments.location_id", locationFilter.locationId);
        } else {
          // No location filtering needed
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
            .select("id");
        }

        // Apply organization filter if needed
        if (locationFilter.shouldFilterOrganization) {
          yesterdayAppointmentsQuery = yesterdayAppointmentsQuery.eq("organization_id", locationFilter.organizationId);
        }

        // Apply date filters
        yesterdayAppointmentsQuery = yesterdayAppointmentsQuery
          .gte("appointment_date", yesterdayDateStr)
          .lt("appointment_date", todayDateStr);

        const { data: yesterdayAppointments, error: yesterdayError } = await yesterdayAppointmentsQuery;

        if (yesterdayError) throw new Error(yesterdayError.message);

        // Fetch patients with proper location filtering
        let patientsQuery = supabase
          .from("patients")
          .select("id, created_at, date_of_birth, gender");

        // Apply location-aware filtering
        patientsQuery = applyLocationFilter(patientsQuery, locationFilter);

        const { data: patients, error: patientsError } = await patientsQuery;

        if (patientsError) throw new Error(patientsError.message);

        // Calculate stats
        const todayCount = (todayAppointments as SimpleAppointment[])?.length || 0;
        const yesterdayCount = (yesterdayAppointments as SimpleAppointment[])?.length || 0;
        const todayChange = yesterdayCount > 0 ? ((todayCount - yesterdayCount) / yesterdayCount) * 100 : 0;

        // Calculate new patients (last 7 days)
        const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const newPatients = (patients as SimplePatient[])?.filter(
          (patient: SimplePatient) => new Date(patient.created_at) >= lastWeek
        ).length || 0;

        // Calculate demographics
        const ageGroups = calculateAgeGroups(patients as SimplePatient[]);
        const genderDistribution = calculateGenderDistribution(patients as SimplePatient[]);

        // Calculate appointment metrics
        const statusMetrics = calculateStatusMetrics(todayAppointments as SimpleAppointment[]);

        setDashboardStats({
          todayAppointments: todayCount,
          todayAppointmentsChange: todayChange,
          newPatients,
          newPatientsChange: 0, // Simplified for now
          totalProcedures: 0, // Simplified for now
          totalProceduresChange: 0,
          revenue: 0, // Simplified for now
          revenueChange: 0,
        });

        setPatientDemographics({
          ageGroups,
          genderDistribution,
        });

        setAppointmentMetrics({
          byStatus: statusMetrics,
          byDepartment: [], // Simplified for now
        });

      } catch (err) {
        console.error("Error fetching analytics:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch analytics");
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalytics();
  }, [user, organization, selectedLocation, locationFilter]);

  return {
    dashboardStats,
    patientDemographics,
    appointmentMetrics,
    isLoading,
    error,
  };
}

function calculateAgeGroups(patients: SimplePatient[]): Array<{ label: string; value: number }> {
  if (!patients?.length) return [];

  const ageGroups = {
    "0-17": 0,
    "18-34": 0,
    "35-54": 0,
    "55-74": 0,
    "75+": 0,
  };

  patients.forEach((patient: SimplePatient) => {
    if (!patient.date_of_birth) return;

    const age = new Date().getFullYear() - new Date(patient.date_of_birth).getFullYear();

    if (age <= 17) ageGroups["0-17"]++;
    else if (age <= 34) ageGroups["18-34"]++;
    else if (age <= 54) ageGroups["35-54"]++;
    else if (age <= 74) ageGroups["55-74"]++;
    else ageGroups["75+"]++;
  });

  return Object.entries(ageGroups).map(([label, value]) => ({ label, value }));
}

function calculateGenderDistribution(patients: SimplePatient[]): Array<{ label: string; value: number }> {
  if (!patients?.length) return [];

  const genderCounts = patients.reduce((acc: Record<string, number>, patient: SimplePatient) => {
    const gender = patient.gender || "Unknown";
    acc[gender] = (acc[gender] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(genderCounts).map(([label, value]) => ({ label, value }));
}

function calculateStatusMetrics(appointments: SimpleAppointment[]): Array<{ label: string; value: number }> {
  if (!appointments?.length) return [];

  const statusCounts = appointments.reduce((acc: Record<string, number>, appointment: SimpleAppointment) => {
    const status = appointment.status || "Unknown";
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(statusCounts).map(([label, value]) => ({ label, value }));
}