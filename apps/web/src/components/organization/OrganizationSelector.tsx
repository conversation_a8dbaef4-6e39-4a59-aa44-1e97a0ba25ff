import { But<PERSON> } from "@/components/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/hooks/useAuth";
import { useOrganization } from "@/hooks/useOrganization";
import { useUserRoles } from "@/hooks/useUserRoles";
import { Building, Building2, ChevronDown, Edit, Home, Search, Stethoscope } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Helper function to get organization icon based on type
const getOrganizationIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'health_system':
    case 'health_network':
      return Building2;
    case 'hospital':
      return Building;
    case 'clinic':
    case 'clinic_group':
      return Stethoscope;
    case 'practice':
    case 'department':
      return Home;
    default:
      return Building2;
  }
};



export function OrganizationSelector() {
  const {
    currentOrg,
    availableOrgs,
    isLoading,
    switchOrganization,
    hasMultipleOrgs,
    isSystemAdmin
  } = useOrganization();
  const { isAdmin } = useUserRoles();
  const { setOrganization } = useAuth();
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [filteredOrganizations, setFilteredOrganizations] = useState(availableOrgs);
  const [searchTerm, setSearchTerm] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Debug logging
  console.log("[ORG_SELECTOR] Available orgs:", availableOrgs.length, availableOrgs.map(org => org.name));
  console.log("[ORG_SELECTOR] Filtered orgs:", filteredOrganizations.length, filteredOrganizations.map(org => org.name));
  console.log("[ORG_SELECTOR] Current org:", currentOrg?.name);
  console.log("[ORG_SELECTOR] Has multiple orgs:", hasMultipleOrgs);
  console.log("[ORG_SELECTOR] Is system admin:", isSystemAdmin);
  console.log("[ORG_SELECTOR] Is admin:", isAdmin);

  // Update filtered organizations when available orgs change
  useEffect(() => {
    setFilteredOrganizations(availableOrgs);
  }, [availableOrgs]);

  // Build hierarchical organization tree and filter based on search term
  useEffect(() => {
    // First, build the hierarchical tree structure
    const buildHierarchicalList = (orgs: typeof availableOrgs) => {
      // Define type for organization with children
      type OrgWithChildren = typeof availableOrgs[0] & { children: OrgWithChildren[] };

      // Create a map for quick lookup
      const orgMap = new Map<string, OrgWithChildren>(
        orgs.map(org => [org.id, { ...org, children: [] as OrgWithChildren[] }])
      );

      // Build the tree structure
      const roots: OrgWithChildren[] = [];

      orgs.forEach(org => {
        const orgWithChildren = orgMap.get(org.id)!;

        if (org.parent_id && orgMap.has(org.parent_id)) {
          // Add to parent's children
          const parent = orgMap.get(org.parent_id)!;
          parent.children.push(orgWithChildren);
        } else {
          // This is a root organization
          roots.push(orgWithChildren);
        }
      });

      // Flatten the tree in hierarchical order
      const flattenTree = (nodes: OrgWithChildren[], level = 0): typeof availableOrgs => {
        const result: typeof availableOrgs = [];

        // Sort nodes at each level by name
        const sortedNodes = [...nodes].sort((a, b) => a.name.localeCompare(b.name));

        sortedNodes.forEach(node => {
          // Add the node itself with correct hierarchy level
          const { children, ...orgWithoutChildren } = node;
          result.push({
            ...orgWithoutChildren,
            hierarchy_level: level
          });

          // Add its children recursively
          if (children && children.length > 0) {
            result.push(...flattenTree(children, level + 1));
          }
        });

        return result;
      };

      return flattenTree(roots);
    };

    // Build hierarchical structure
    const hierarchicalOrgs = buildHierarchicalList(availableOrgs);

    // Apply search filter
    if (!searchTerm) {
      setFilteredOrganizations(hierarchicalOrgs);
    } else {
      const filtered = hierarchicalOrgs.filter(
        (org) =>
          org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          org.type?.toLowerCase().includes(searchTerm.toLowerCase()),
      );
      setFilteredOrganizations(filtered);
    }

    // Reset to first item when search changes
    setHighlightedIndex(0);
  }, [searchTerm, availableOrgs]);

  const handleOrganizationSelect = async (orgId: string) => {
    try {
      setIsDropdownOpen(false);

      // Update both the Zustand store AND the auth context
      await switchOrganization(orgId, true); // true = update URL
      const selectedOrg = availableOrgs.find(org => org.id === orgId);

      if (selectedOrg) {
        // Also update the auth context so NavigationManager sees the change immediately
        setOrganization(selectedOrg);
        toast.success(`Switched to ${selectedOrg?.name}`);
      } else {
        toast.error("Organization not found");
      }
    } catch (err) {
      console.error("Failed to switch organization:", err);
      toast.error("Failed to switch organization");
    }
  };

  // Handle edit organization click
  const handleEditOrganization = (e: React.MouseEvent, orgId: string) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/organizations/${orgId}/settings`);
  };

  const canSwitchOrgs = isAdmin && (isSystemAdmin || hasMultipleOrgs);

  if (!currentOrg && availableOrgs.length === 0) {
    return (
      <Button
        variant="outline"
        className="h-9 px-3 py-2 flex items-center gap-2 w-full lg:w-[220px] xl:w-[280px] border-input"
        disabled
        title="No Organization"
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <span className="truncate font-medium text-left">No Organization</span>
        </div>
      </Button>
    );
  }

  if (!canSwitchOrgs) {
    return (
      <Button
        variant="outline"
        className="h-9 px-3 py-2 flex items-center gap-2 w-full lg:w-[220px] xl:w-[280px] border-input"
        disabled={isLoading}
        title={currentOrg?.name || "Select Organization"} // Add tooltip
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
          <span className="truncate font-medium text-left">
            {currentOrg?.name || "Select Organization"}
          </span>
        </div>
      </Button>
    );
  }

  return (
    <DropdownMenu
      open={isDropdownOpen}
      onOpenChange={(open) => {
        setIsDropdownOpen(open);
        if (!open) {
          setTimeout(() => {
            setSearchTerm("");
            setHighlightedIndex(-1);
          }, 150);
        } else {
          setTimeout(() => {
            searchInputRef.current?.focus();
          }, 100);
        }
      }}
    >
      <DropdownMenuTrigger asChild disabled={isLoading}>
        <Button
          variant="outline"
          className="h-9 px-3 py-2 flex items-center justify-between gap-2 w-full lg:w-[220px] xl:w-[280px] border-input hover:ring-1 hover:ring-ring/30 hover:ring-offset-0 hover:bg-accent/50 focus-visible:ring-1 focus-visible:ring-ring/30 focus-visible:ring-offset-0 data-[state=open]:ring-1 data-[state=open]:ring-ring/30 data-[state=open]:ring-offset-0 data-[state=open]:bg-accent/50"
          title={currentOrg?.name || "Select Organization"} // Add tooltip
        >
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Building2 className="h-5 w-5 text-muted-foreground flex-shrink-0" />
            <span className="truncate font-medium text-left">
              {currentOrg?.name || "Select Organization"}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="w-[calc(100vw-2rem)] max-w-[380px] min-w-[300px] border-input shadow-sm"
        sideOffset={4}
      >
        <DropdownMenuLabel>Switch Organization</DropdownMenuLabel>
        <DropdownMenuSeparator className="opacity-50" />

        {/* Search Input - only show if there are multiple organizations */}
        {availableOrgs.length > 3 && (
          <>
            <div
              className="px-2 py-1"
              onKeyDown={(e) => {
                // Stop all keyboard events from bubbling up to the dropdown
                e.stopPropagation();
              }}
            >
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    // Handle keyboard navigation
                    if (e.key === "ArrowDown") {
                      e.preventDefault();
                      setHighlightedIndex(prev =>
                        prev < filteredOrganizations.length - 1 ? prev + 1 : 0
                      );
                    } else if (e.key === "ArrowUp") {
                      e.preventDefault();
                      setHighlightedIndex(prev =>
                        prev > 0 ? prev - 1 : filteredOrganizations.length - 1
                      );
                    } else if (e.key === "Enter") {
                      e.preventDefault();
                      if (highlightedIndex >= 0 && filteredOrganizations[highlightedIndex]) {
                        handleOrganizationSelect(filteredOrganizations[highlightedIndex].id);
                      }
                    } else if (e.key === "Escape") {
                      e.preventDefault();
                      setIsDropdownOpen(false);
                    } else {
                      // For typing, prevent dropdown menu from handling these keys
                      e.stopPropagation();
                    }
                  }}
                  onFocus={(e) => {
                    // Prevent dropdown from closing when input is focused
                    e.stopPropagation();
                  }}
                  className="pl-8 h-8 bg-transparent border-input focus-visible:ring-1 focus-visible:ring-ring/30 focus-visible:ring-offset-0"
                  autoComplete="off"
                />
              </div>
            </div>
            <DropdownMenuSeparator className="opacity-50" />
          </>
        )}

        {/* Organizations List */}
        <div ref={dropdownRef} className="max-h-[300px] overflow-y-auto">
          {filteredOrganizations.map((org, index) => {
            const isCurrent = currentOrg && currentOrg.id === org.id;
            const Icon = getOrganizationIcon(org.type);
            const indentLevel = (org.hierarchy_level || 0) * 16; // 16px per level

            return (
              <DropdownMenuItem
                key={org.id}
                disabled={isLoading || !!isCurrent}
                className={`flex items-center justify-between data-[highlighted]:bg-muted/50 hover:!bg-muted/50 ${
                  highlightedIndex === index ? "bg-accent text-accent-foreground" : ""
                }`}
                onSelect={(e) => {
                  e.preventDefault();
                  handleOrganizationSelect(org.id);
                }}
                onMouseEnter={() => setHighlightedIndex(index)}
                data-index={index}
              >
                <div className="flex items-center gap-2 flex-1" style={{ paddingLeft: `${indentLevel}px` }}>
                  <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex flex-col min-w-0 flex-1">
                    <span className={`truncate ${org.hierarchy_level === 0 ? 'font-semibold' : 'font-medium'}`}>
                      {org.name}
                    </span>
                    {org.type !== 'system' && (
                      <span className="text-xs text-muted-foreground capitalize">
                        {org.type.replace('_', ' ')}
                      </span>
                    )}
                  </div>
                  {isCurrent && (
                    <span className="ml-2 text-xs text-emerald-500/60 font-medium">
                      (Current)
                    </span>
                  )}
                </div>
                {isSystemAdmin && (
                  <Button
                    data-edit="true"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 opacity-60 hover:opacity-100 hover:!bg-muted/50"
                    onMouseDown={(e) => handleEditOrganization(e, org.id)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                )}
              </DropdownMenuItem>
            );
          })}

          {filteredOrganizations.length === 0 && searchTerm && (
            <div className="px-2 py-4 text-center text-sm text-muted-foreground">
              No organizations found matching "{searchTerm}"
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
